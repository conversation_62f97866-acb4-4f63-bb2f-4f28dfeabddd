<template>
  <c-col col="12" lg="12">
    <c-card>
      <c-card-header>
        <div class="d-flex justify-content-between align-items-center">
          <h4 class="mb-0">
            <c-icon name="cil-chart-line" class="text-primary me-2"></c-icon>
            Helicopter View Report
          </h4>
          <div class="d-flex align-items-center">
            <c-badge 
              v-if="isLoading" 
              color="info" 
              class="me-2"
            >
              <c-icon name="cil-reload" class="spin me-1"></c-icon>
              Loading...
            </c-badge>
            <c-badge 
              v-else-if="reportData && reportData.length > 0" 
              color="success" 
              class="me-2"
            >
              {{ reportData.length }} Records
            </c-badge>
          </div>
        </div>
      </c-card-header>
      
      <c-card-body>
        <!-- Filters Section -->
        <c-card class="mb-4">
          <c-card-header class="bg-light">
            <h6 class="mb-0">
              <c-icon name="cil-filter" class="me-2"></c-icon>
              Report Filters
            </h6>
          </c-card-header>
          <c-card-body>
            <div class="row">
              <!-- Date Range -->
              <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                <c-form-group>
                  <template #label>
                    <strong>From Date</strong>
                    <span class="text-danger">*</span>
                  </template>
                  <template #input>
                    <c-input 
                      type="date" 
                      v-model="filters.from_date"
                      :max="filters.to_date || getCurrentDate()"
                      required
                    />
                  </template>
                </c-form-group>
              </div>
              
              <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                <c-form-group>
                  <template #label>
                    <strong>To Date</strong>
                    <span class="text-danger">*</span>
                  </template>
                  <template #input>
                    <c-input 
                      type="date" 
                      v-model="filters.to_date"
                      :min="filters.from_date"
                      :max="getCurrentDate()"
                      required
                    />
                  </template>
                </c-form-group>
              </div>
              
              <!-- Lines Selection -->
              <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
                <c-form-group>
                  <template #label>
                    <strong>Lines</strong>
                    <span class="text-danger">*</span>
                  </template>
                  <template #input>
                    <div class="d-flex align-items-center mb-2">
                      <input
                        v-if="lines.length > 0"
                        id="checkAllLines"
                        type="checkbox"
                        v-model="checkAllLines"
                        @change="toggleAllLines"
                        class="me-2"
                      />
                      <label 
                        v-if="lines.length > 0" 
                        for="checkAllLines" 
                        class="mb-0 fw-bold"
                      >
                        Select All Lines
                      </label>
                    </div>
                    <v-select
                      v-model="filters.line_ids"
                      :options="lines"
                      label="name"
                      :reduce="(line) => line.id"
                      placeholder="Select Lines"
                      multiple
                      :loading="loadingLines"
                      :disabled="loadingLines"
                    >
                      <template #no-options>
                        {{ loadingLines ? 'Loading lines...' : 'No lines available' }}
                      </template>
                    </v-select>
                  </template>
                </c-form-group>
              </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="row">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                  <c-button 
                    color="secondary" 
                    @click="resetFilters"
                    :disabled="isLoading"
                  >
                    <c-icon name="cil-reload" class="me-1"></c-icon>
                    Reset
                  </c-button>
                  <c-button 
                    color="primary" 
                    @click="generateReport"
                    :disabled="!canGenerateReport || isLoading"
                  >
                    <c-icon name="cil-chart-line" class="me-1"></c-icon>
                    Generate Report
                  </c-button>
                </div>
              </div>
            </div>
          </c-card-body>
        </c-card>

        <!-- Loading State -->
        <div v-if="isLoading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-3 text-muted">Generating helicopter view report...</p>
        </div>

        <!-- Error State -->
        <c-alert 
          v-if="error && !isLoading" 
          color="danger" 
          :show="true"
          class="mb-4"
        >
          <c-icon name="cil-warning" class="me-2"></c-icon>
          {{ error }}
        </c-alert>

        <!-- Report Results -->
        <div v-if="reportData && reportData.length > 0 && !isLoading">
          <!-- Summary Cards -->
          <div class="row mb-4" v-if="summaryData">
            <div class="col-lg-3 col-md-6 col-sm-12 mb-3" v-for="(summary, index) in summaryCards" :key="index">
              <c-card class="h-100 border-0 shadow-sm">
                <c-card-body class="text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <c-icon :name="summary.icon" :class="`text-${summary.color} me-2`" size="lg"></c-icon>
                    <h6 class="mb-0 text-muted">{{ summary.title }}</h6>
                  </div>
                  <h3 :class="`text-${summary.color} mb-0`">{{ summary.value }}</h3>
                  <small v-if="summary.subtitle" class="text-muted">{{ summary.subtitle }}</small>
                </c-card-body>
              </c-card>
            </div>
          </div>

          <!-- Data Table -->
          <c-card>
            <c-card-header class="d-flex justify-content-between align-items-center">
              <h6 class="mb-0">
                <c-icon name="cil-list" class="me-2"></c-icon>
                Report Data
              </h6>
              <div class="d-flex gap-2">
                <c-button 
                  color="success" 
                  size="sm" 
                  @click="downloadExcel"
                  :disabled="isLoading"
                >
                  <c-icon name="cil-cloud-download" class="me-1"></c-icon>
                  Excel
                </c-button>
                <c-button 
                  color="info" 
                  size="sm" 
                  @click="printReport"
                  :disabled="isLoading"
                >
                  <c-icon name="cil-print" class="me-1"></c-icon>
                  Print
                </c-button>
              </div>
            </c-card-header>
            <c-card-body class="p-0">
              <div class="table-responsive">
                <table class="table table-hover mb-0" id="helicopterViewTable">
                  <thead class="table-dark sticky-top">
                    <tr>
                      <th v-for="field in tableFields" :key="field.key" :class="field.class">
                        {{ field.label }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in reportData" :key="index">
                      <td v-for="field in tableFields" :key="field.key" :class="field.class">
                        <span v-if="field.formatter">
                          {{ field.formatter(item[field.key], item) }}
                        </span>
                        <span v-else>
                          {{ item[field.key] }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </c-card-body>
          </c-card>
        </div>

        <!-- Empty State -->
        <div v-if="!reportData || reportData.length === 0 && !isLoading && !error" class="text-center py-5">
          <c-icon name="cil-chart-line" size="3xl" class="text-muted mb-3"></c-icon>
          <h5 class="text-muted">No Data Available</h5>
          <p class="text-muted">
            {{ hasGeneratedReport ? 'No data found for the selected criteria.' : 'Select filters and click "Generate Report" to view data.' }}
          </p>
        </div>
      </c-card-body>
    </c-card>
  </c-col>
</template>

<script>
import moment from 'moment';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';

export default {
  name: 'HelicopterViewReport',
  components: {
    vSelect,
  },
  data() {
    return {
      // Loading states
      isLoading: false,
      loadingLines: false,

      // Error handling
      error: null,

      // Filters
      filters: {
        from_date: moment().startOf('month').format('YYYY-MM-DD'),
        to_date: moment().endOf('month').format('YYYY-MM-DD'),
        line_ids: [],
      },

      // Data
      lines: [],
      reportData: [],
      summaryData: null,
      tableFields: [],

      // UI state
      checkAllLines: false,
      hasGeneratedReport: false,

      // Report name for downloads
      reportName: 'Helicopter View Report',
    };
  },
  computed: {
    canGenerateReport() {
      return (
        this.filters.from_date &&
        this.filters.to_date &&
        this.filters.line_ids &&
        this.filters.line_ids.length > 0
      );
    },

    summaryCards() {
      if (!this.summaryData) return [];

      return [
        {
          title: 'Total Records',
          value: this.summaryData.total_records || this.reportData.length,
          icon: 'cil-list',
          color: 'primary',
          subtitle: 'Data points'
        },
        {
          title: 'Lines Covered',
          value: this.summaryData.lines_count || this.filters.line_ids.length,
          icon: 'cil-layers',
          color: 'success',
          subtitle: 'Business lines'
        },
        {
          title: 'Date Range',
          value: this.getDateRangeDays(),
          icon: 'cil-calendar',
          color: 'info',
          subtitle: 'Days covered'
        },
        {
          title: 'Last Updated',
          value: moment().format('HH:mm'),
          icon: 'cil-clock',
          color: 'warning',
          subtitle: 'Today'
        }
      ];
    },
  },
  methods: {
    async initialize() {
      await this.loadLines();
    },

    async loadLines() {
      this.loadingLines = true;
      this.error = null;

      try {
        const { res, error } = await this.tryCatch(
          axios.get('/api/lines')
        );

        if (error) {
          this.showErrorMessage(error);
          return;
        }

        this.lines = res.data.lines;

        // Auto-select all lines if user has permission
        if (this.lines.length > 0) {
          this.filters.line_ids = this.lines.map(line => line.id);
          this.checkAllLines = true;
        }
      } catch (err) {
        console.error('Error loading lines:', err);
        this.error = 'Failed to load lines. Please refresh the page.';
      } finally {
        this.loadingLines = false;
      }
    },

    async generateReport() {
      if (!this.canGenerateReport) {
        this.flash('Please select date range and at least one line.', 'warning');
        return;
      }

      this.isLoading = true;
      this.error = null;
      this.hasGeneratedReport = true;

      try {
        const payload = {
          from_date: this.filters.from_date,
          to_date: this.filters.to_date,
          line_ids: this.filters.line_ids,
        };

        const { res, error } = await this.tryCatch(
          axios.post('/api/helicopter-view-report', payload)
        );

        if (error) {
          this.showErrorMessage(error);
          return;
        }

        const responseData = res.data;

        // Handle response structure
        this.reportData = responseData.data.metrics;
        this.tableFields = responseData.fields || this.generateDefaultFields();

        // Show success message
        this.flash(
          `Report generated successfully with ${this.reportData.length} records.`,
          'success'
        );

      } catch (err) {
        console.error('Error generating report:', err);
        this.error = 'Failed to generate report. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },

    generateDefaultFields() {
      if (!this.reportData || this.reportData.length === 0) return [];

      // Generate fields from first data item
      const firstItem = this.reportData[0];
      return Object.keys(firstItem).map(key => ({
        key,
        label: this.formatFieldLabel(key),
        class: this.getFieldClass(key),
        formatter: this.getFieldFormatter(key),
      }));
    },

    formatFieldLabel(key) {
      return key
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    },

    getFieldClass(key) {
      if (key.includes('amount') || key.includes('total') || key.includes('value')) {
        return 'text-end';
      }
      if (key.includes('date')) {
        return 'text-center';
      }
      return '';
    },

    getFieldFormatter(key) {
      if (key.includes('amount') || key.includes('total') || key.includes('value')) {
        return (value) => this.formatCurrency(value);
      }
      if (key.includes('date')) {
        return (value) => this.formatDate(value);
      }
      if (key.includes('percentage') || key.includes('rate')) {
        return (value) => this.formatPercentage(value);
      }
      return null;
    },

    formatCurrency(value) {
      if (value === null || value === undefined) return '-';
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
      }).format(value);
    },

    formatDate(value) {
      if (!value) return '-';
      return moment(value).format('DD/MM/YYYY');
    },

    formatPercentage(value) {
      if (value === null || value === undefined) return '-';
      return `${parseFloat(value).toFixed(2)}%`;
    },

    toggleAllLines() {
      if (this.checkAllLines) {
        this.filters.line_ids = this.lines.map(line => line.id);
      } else {
        this.filters.line_ids = [];
      }
    },

    resetFilters() {
      this.filters = {
        from_date: moment().startOf('month').format('YYYY-MM-DD'),
        to_date: moment().endOf('month').format('YYYY-MM-DD'),
        line_ids: this.lines.map(line => line.id),
      };
      this.checkAllLines = true;
      this.reportData = [];
      this.summaryData = null;
      this.tableFields = [];
      this.error = null;
      this.hasGeneratedReport = false;
    },

    getCurrentDate() {
      return moment().format('YYYY-MM-DD');
    },

    getDateRangeDays() {
      if (!this.filters.from_date || !this.filters.to_date) return 0;
      const start = moment(this.filters.from_date);
      const end = moment(this.filters.to_date);
      return end.diff(start, 'days') + 1;
    },

    downloadExcel() {
      if (!this.reportData || this.reportData.length === 0) {
        this.flash('No data available to download.', 'warning');
        return;
      }

      try {
        const filename = `${this.reportName}_${moment().format('YYYY-MM-DD_HH-mm')}.xlsx`;
        this.downloadXlsx(this.reportData, filename);
        this.flash('Excel file downloaded successfully.', 'success');
      } catch (error) {
        console.error('Error downloading Excel:', error);
        this.flash('Failed to download Excel file.', 'error');
      }
    },

    printReport() {
      try {
        this.$htmlToPaper('helicopterViewTable');
      } catch (error) {
        console.error('Error printing report:', error);
        this.flash('Failed to print report.', 'error');
      }
    },
  },
  watch: {
    'filters.line_ids': {
      handler(newVal) {
        this.checkAllLines = newVal && newVal.length === this.lines.length;
      },
      deep: true,
    },
  },
  async created() {
    await this.initialize();
  },
};
</script>

<style scoped>
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.table-responsive {
  max-height: 600px;
  overflow-y: auto;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 10;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

/* Custom card hover effects */
.card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* Loading spinner customization */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Table hover effects */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

/* Button group spacing */
.btn + .btn {
  margin-left: 0.5rem;
}

/* Form validation styles */
.is-invalid {
  border-color: #dc3545;
}

.is-valid {
  border-color: #28a745;
}

/* Custom alert styles */
.alert {
  border-radius: 0.375rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
  }

  .gap-2 > * {
    margin-bottom: 0.5rem;
  }

  .gap-2 > *:last-child {
    margin-bottom: 0;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    border: none !important;
    box-shadow: none !important;
  }

  .table {
    font-size: 12px;
  }
}
</style>
