<template>
  <c-card>
    <c-card-header>Helicopter View Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-line" /> Main Filters
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input 
                    label="From Date" 
                    type="date" 
                    placeholder="From Date" 
                    v-model="from_date"
                    :max="getCurrentDate()"
                  />
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input 
                    label="To Date" 
                    type="date" 
                    placeholder="To Date" 
                    v-model="to_date"
                    :max="getCurrentDate()"
                    @input="getAllData"
                  />
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Report Type</strong>
                    </label>
                    <v-select 
                      v-model="report_type" 
                      :options="reportTypes" 
                      label="name" 
                      :reduce="(type) => type.value"
                      placeholder="Select Report Type"
                    />
                  </div>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-layers" /> Line Selection
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-12">
                  <div class="form-group">
                    <label>
                      <strong>Lines</strong>
                    </label>
                    <select-all-checkbox :items="lines" v-model="checkAllLines" />
                    <v-select 
                      v-model="line_ids" 
                      :options="lines" 
                      label="name" 
                      :reduce="(line) => line.id"
                      placeholder="Select Lines" 
                      multiple 
                      :filterable="true"
                    />
                  </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                  <div class="form-group">
                    <label>
                      <strong>Divisions</strong>
                    </label>
                    <select-all-checkbox :items="divisions" v-model="checkAllDivisions" />
                    <v-select 
                      v-model="div_ids" 
                      :options="divisions" 
                      label="name" 
                      :reduce="(division) => division.id"
                      placeholder="Select Divisions" 
                      multiple 
                      :filterable="true"
                    />
                  </div>
                </div>
              </div>

              <!-- Additional Filters Row -->
              <div class="row mt-3">
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>
                      <strong>Aggregation Level</strong>
                    </label>
                    <v-select 
                      v-model="aggregation_level" 
                      :options="aggregationLevels" 
                      label="name" 
                      :reduce="(level) => level.value"
                      placeholder="Select Aggregation"
                    />
                  </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>
                      <strong>Include Metrics</strong>
                    </label>
                    <v-select 
                      v-model="include_metrics" 
                      :options="availableMetrics" 
                      label="name" 
                      :reduce="(metric) => metric.value"
                      placeholder="Select Metrics" 
                      multiple
                    />
                  </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>
                      <strong>
                        <c-icon class="custom_icon" name="cil-options" />
                        Options
                      </strong>
                    </label>
                    <div class="mt-2">
                      <div class="form-check">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="include-summary"
                          v-model="include_summary"
                        />
                        <label class="form-check-label" for="include-summary">
                          Include Summary Statistics
                        </label>
                      </div>
                      <div class="form-check">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="include-trends"
                          v-model="include_trends"
                        />
                        <label class="form-check-label" for="include-trends">
                          Include Trend Analysis
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button 
        color="primary" 
        class="text-white" 
        @click="show" 
        style="float: right" 
        :disabled="isLoading || !canGenerateReport"
      >
        <c-spinner v-if="isLoading" size="sm" class="mr-2" />
        {{ isLoading ? 'Loading...' : 'Generate Report' }}
      </c-button>
      <c-button 
        color="secondary" 
        @click="resetFilters" 
        style="float: right; margin-right: 10px;"
        :disabled="isLoading"
      >
        Reset Filters
      </c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import SelectAllCheckbox from "../../common/SelectAllCheckBox.vue";
import { showErrorMessage } from "../../../mixins/ShowErrorMessage";

export default {
  components: {
    SelectAllCheckbox,
    vSelect,
  },
  mixins: [showErrorMessage],
  emits: ["getHelicopterViewReport"],
  data() {
    return {
      // Filter values
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
      report_type: "overview",
      aggregation_level: "daily",
      include_metrics: ["visits", "sales", "coverage"],
      include_summary: true,
      include_trends: false,

      // Selection
      div_ids: [],
      line_ids: [],

      // Data arrays
      divisions: [],
      lines: [],

      // Checkbox states
      checkAllDivisions: false,
      checkAllLines: false,

      // Loading state
      isLoading: false,

      // Options
      reportTypes: [
        { name: "Overview Report", value: "overview" },
        { name: "Performance Analysis", value: "performance" },
        { name: "Trend Analysis", value: "trends" },
        { name: "Comparative Analysis", value: "comparative" }
      ],

      aggregationLevels: [
        { name: "Daily", value: "daily" },
        { name: "Weekly", value: "weekly" },
        { name: "Monthly", value: "monthly" },
        { name: "Quarterly", value: "quarterly" }
      ],

      availableMetrics: [
        { name: "Visits Count", value: "visits" },
        { name: "Sales Amount", value: "sales" },
        { name: "Coverage Percentage", value: "coverage" },
        { name: "Performance Score", value: "performance" },
        { name: "Growth Rate", value: "growth" },
        { name: "Target Achievement", value: "targets" }
      ]
    };
  },
  computed: {
    canGenerateReport() {
      return (
        this.from_date &&
        this.to_date &&
        this.line_ids &&
        this.line_ids.length > 0 &&
        this.include_metrics &&
        this.include_metrics.length > 0
      );
    }
  },
  methods: {
    initialize() {
      this.isLoading = true;
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'divisions']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.divisions = response.data.data.divisions || [];
          
          // Auto-select all lines if user has permission
          if (this.lines.length > 0) {
            this.line_ids = this.lines.map(line => line.id);
            this.checkAllLines = true;
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    getAllData() {
      this.line_ids = [];
      this.div_ids = [];
      this.initialize();
    },

    getLineData() {
      if (this.line_ids.length === 0) {
        this.divisions = [];
        return;
      }

      this.isLoading = true;
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_ids,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions || [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    resetFilters() {
      this.from_date = moment().startOf("month").format("YYYY-MM-DD");
      this.to_date = moment().endOf("month").format("YYYY-MM-DD");
      this.report_type = "overview";
      this.aggregation_level = "daily";
      this.include_metrics = ["visits", "sales", "coverage"];
      this.include_summary = true;
      this.include_trends = false;
      this.div_ids = [];
      this.line_ids = this.lines.map(line => line.id);
      this.checkAllLines = true;
      this.checkAllDivisions = false;
    },

    getCurrentDate() {
      return moment().format("YYYY-MM-DD");
    },

    show() {
      if (!this.canGenerateReport) {
        this.flash('Please select date range, at least one line, and at least one metric.', 'warning');
        return;
      }

      const filters = {
        from_date: this.from_date,
        to_date: this.to_date,
        line_ids: this.line_ids,
        div_ids: this.div_ids,
        report_type: this.report_type,
        aggregation_level: this.aggregation_level,
        include_metrics: this.include_metrics,
        include_summary: this.include_summary,
        include_trends: this.include_trends,
      };

      this.$emit("getHelicopterViewReport", { filters });
    },
  },
  watch: {
    line_ids: {
      handler() {
        this.getLineData();
      },
      deep: true,
    },
    checkAllLines(value) {
      if (value) this.line_ids = this.lines.map((item) => item.id);
      if (!value) this.line_ids = [];
    },
    checkAllDivisions(value) {
      if (value) this.div_ids = this.divisions.map((item) => item.id);
      if (!value) this.div_ids = [];
    },
  },
  created() {
    this.initialize();
  },
};
</script>

<style scoped>
.custom_icon {
  margin-right: 5px;
}

.form-check {
  margin-bottom: 0.5rem;
}

.form-check-label {
  font-size: 0.875rem;
}
</style>
